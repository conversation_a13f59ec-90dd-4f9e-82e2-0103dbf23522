<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

$backup_dir = 'backups/';
$message = '';
$message_type = '';

// Handle restore from existing file
if (isset($_POST['restore_file'])) {
    $file_to_restore = $backup_dir . basename($_POST['backup_select']);
    if (file_exists($file_to_restore) && pathinfo($file_to_restore, PATHINFO_EXTENSION) == 'sql') {
        require_once 'config.php';
        $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($mysqli->connect_error) {
            $message = "数据库连接失败: " . $mysqli->connect_error;
            $message_type = 'error';
        } else {
            $sql_script = file_get_contents($file_to_restore);
            if ($mysqli->multi_query($sql_script)) {
                // Clear remaining results from multi_query
                do {
                    if ($result = $mysqli->store_result()) {
                        $result->free();
                    }
                } while ($mysqli->next_result());
                $message = "从文件 " . htmlspecialchars(basename($file_to_restore)) . " 恢复数据库成功！";
                $message_type = 'success';
            } else {
                $message = "数据库恢复失败: " . $mysqli->error;
                $message_type = 'error';
            }
            $mysqli->close();
        }
    } else {
        $message = "错误：选择的备份文件无效或不存在。";
        $message_type = 'error';
    }
}

// Handle restore from uploaded file
if (isset($_POST['restore_upload'])) {
    if (isset($_FILES['backup_upload']) && $_FILES['backup_upload']['error'] == UPLOAD_ERR_OK) {
        $file_tmp_path = $_FILES['backup_upload']['tmp_name'];
        $file_name = $_FILES['backup_upload']['name'];
        if (pathinfo($file_name, PATHINFO_EXTENSION) == 'sql') {
            require_once 'config.php';
            $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            if ($mysqli->connect_error) {
                $message = "数据库连接失败: " . $mysqli->connect_error;
                $message_type = 'error';
            } else {
                $sql_script = file_get_contents($file_tmp_path);
                if ($mysqli->multi_query($sql_script)) {
                    do {
                        if ($result = $mysqli->store_result()) {
                            $result->free();
                        }
                    } while ($mysqli->next_result());
                    $message = "从上传的文件 " . htmlspecialchars($file_name) . " 恢复数据库成功！";
                    $message_type = 'success';
                } else {
                    $message = "数据库恢复失败: " . $mysqli->error;
                    $message_type = 'error';
                }
                $mysqli->close();
            }
        } else {
            $message = "错误：请上传有效的 .sql 文件。";
            $message_type = 'error';
        }
    } else {
        $message = "文件上传失败。";
        $message_type = 'error';
    }
}

$backup_files = array_diff(scandir($backup_dir), array('..', '.'));
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据恢复</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .warning { background-color: rgba(255, 77, 77, 0.2); border: 1px solid #ff4d4d; color: #ff4d4d; padding: 15px; margin-bottom: 20px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: bold; text-transform: uppercase; }
        .form-group select, .form-group input[type="file"] { width: 100%; padding: 12px; border-radius: 0; border: 1px solid #00aaff; background: rgba(0, 229, 255, 0.1); color: #fff; box-sizing: border-box; font-family: 'Roboto Mono', monospace; }
        .form-group select option { background: #0a0a14; color: #00e5ff; }
        .message { padding: 15px; margin-bottom: 20px; border: 1px solid; text-align: center; }
        .message.success { background-color: rgba(0, 229, 255, 0.1); border-color: #00e5ff; color: #fff; }
        .message.error { background-color: rgba(255, 77, 77, 0.2); border-color: #ff4d4d; color: #ff4d4d; }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>管理菜单</h2>
        <a href="c.php">控制台</a>
        <a href="user.php">会员管理</a>
        <a href="add.php">添加会员</a>
        <a href="backup.php">数据备份</a>
        <a href="restore.php" class="active">数据恢复</a>
        <a href="close.php">安全退出</a>
    </div>
    <div class="main-content">
        <header>
            <h1>数据恢复</h1>
        </header>
        <div class="container">
            <?php if ($message): ?>
                <div class="message <?php echo $message_type; ?>"><?php echo $message; ?></div>
            <?php endif; ?>
            <div class="card">
                <h2>从现有备份恢复</h2>
                <div class="warning"><strong>警告：</strong>此操作将覆盖当前所有数据，且不可逆！请谨慎操作。</div>
                <form action="restore.php" method="post" onsubmit="return confirm('您确定要从选定文件恢复数据库吗？当前所有数据都将被覆盖！');">
                    <div class="form-group">
                        <label for="backup_select">选择备份文件</label>
                        <select name="backup_select" id="backup_select">
                            <?php foreach ($backup_files as $file): ?>
                                <option value="<?php echo htmlspecialchars($file); ?>"><?php echo htmlspecialchars($file); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" name="restore_file" class="btn">恢复选中文件</button>
                </form>
            </div>
            <div class="card">
                <h2>从文件上传恢复</h2>
                <div class="warning"><strong>警告：</strong>确保您上传的是本系统生成的有效SQL备份文件。</div>
                <form action="restore.php" method="post" enctype="multipart/form-data" onsubmit="return confirm('您确定要从上传的文件恢复数据库吗？当前所有数据都将被覆盖！');">
                    <div class="form-group">
                        <label for="backup_upload">上传 .sql 文件</label>
                        <input type="file" name="backup_upload" id="backup_upload" accept=".sql" required>
                    </div>
                    <button type="submit" name="restore_upload" class="btn">上传并恢复</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
