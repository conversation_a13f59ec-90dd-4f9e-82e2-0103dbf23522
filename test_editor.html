<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>富文本编辑器测试</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-content {
            margin: 20px 0;
            padding: 15px;
            background: #f9f9f9;
            border-left: 4px solid #007cba;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>富文本编辑器HTML内容测试</h1>
        
        <div class="test-content">
            <h3>测试说明：</h3>
            <p>1. 在下面的编辑器中输入一些带有HTML标签的内容</p>
            <p>2. 点击"Source"按钮可以直接编辑HTML代码</p>
            <p>3. 点击"获取内容"按钮查看编辑器中的HTML内容</p>
            <p>4. 测试各种HTML标签是否能正常保存和显示</p>
        </div>

        <form>
            <textarea name="editor1" id="editor1" rows="10" cols="80">
                <h2 style="color: red;">这是一个测试标题</h2>
                <p>这是一个包含<strong>粗体</strong>和<em>斜体</em>的段落。</p>
                <ul>
                    <li>列表项目 1</li>
                    <li>列表项目 2</li>
                </ul>
                <div style="background-color: yellow; padding: 10px;">
                    这是一个带有样式的DIV元素
                </div>
                <table border="1">
                    <tr>
                        <td>表格单元格1</td>
                        <td>表格单元格2</td>
                    </tr>
                </table>
            </textarea>
            <br>
            <button type="button" class="btn" onclick="getEditorContent()">获取内容</button>
            <button type="button" class="btn" onclick="setTestContent()">设置测试内容</button>
            <button type="button" class="btn" onclick="clearContent()">清空内容</button>
        </form>

        <div id="result" class="result" style="display: none;">
            <h3>编辑器内容：</h3>
            <pre id="content-display"></pre>
            <h3>渲染效果：</h3>
            <div id="rendered-content"></div>
        </div>

        <script>
            // 初始化CKEditor
            CKEDITOR.replace('editor1');

            function getEditorContent() {
                var content = CKEDITOR.instances.editor1.getData();
                document.getElementById('content-display').textContent = content;
                document.getElementById('rendered-content').innerHTML = content;
                document.getElementById('result').style.display = 'block';
            }

            function setTestContent() {
                var testHtml = '<h1 style="color: blue;">测试HTML内容</h1>' +
                              '<p>这是一个包含<span style="background-color: yellow;">高亮文本</span>的段落。</p>' +
                              '<blockquote style="border-left: 3px solid #ccc; padding-left: 15px; margin: 15px 0;">' +
                              '这是一个引用块，包含特殊样式。' +
                              '</blockquote>' +
                              '<div class="custom-class" data-custom="value">' +
                              '这是一个带有自定义属性的DIV' +
                              '</div>';
                
                CKEDITOR.instances.editor1.setData(testHtml);
            }

            function clearContent() {
                CKEDITOR.instances.editor1.setData('');
                document.getElementById('result').style.display = 'none';
            }
        </script>
    </div>
</body>
</html>
