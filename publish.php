<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

require_once 'config.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['editor1'])) {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    $content = $_POST['editor1'];
    $user = 'system';

    // Check if system content already exists
    $stmt = $conn->prepare("SELECT id FROM content WHERE user = ?");
    $stmt->bind_param("s", $user);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        // Update existing content
        $update_stmt = $conn->prepare("UPDATE content SET content = ? WHERE user = ?");
        $update_stmt->bind_param("ss", $content, $user);
        $update_stmt->execute();
        $update_stmt->close();
    } else {
        // Insert new content
        $insert_stmt = $conn->prepare("INSERT INTO content (user, content) VALUES (?, ?)");
        $insert_stmt->bind_param("ss", $user, $content);
        $insert_stmt->execute();
        $insert_stmt->close();
    }

    $stmt->close();
    $conn->close();
}

header('Location: c.php');
exit;
?>
