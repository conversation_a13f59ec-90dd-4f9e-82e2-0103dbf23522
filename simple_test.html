<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>CKEditor HTML保存测试</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .btn { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #ccc; }
        pre { background: #f8f8f8; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CKEditor HTML代码保存测试</h1>
        
        <p><strong>测试步骤：</strong></p>
        <ol>
            <li>点击"Source"按钮切换到源代码模式</li>
            <li>输入或修改HTML代码</li>
            <li>点击"Source"按钮切换回可视化模式</li>
            <li>点击"获取内容"查看是否保留了HTML代码</li>
        </ol>

        <textarea name="editor1" id="editor1" rows="10" cols="80">
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;测试页面&lt;/title&gt;
    &lt;style&gt;
        body { background: #f0f0f0; }
        .highlight { color: red; font-weight: bold; }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;这是测试标题&lt;/h1&gt;
    &lt;p class="highlight"&gt;这是高亮段落&lt;/p&gt;
    &lt;div id="content"&gt;
        &lt;ul&gt;
            &lt;li&gt;列表项1&lt;/li&gt;
            &lt;li&gt;列表项2&lt;/li&gt;
        &lt;/ul&gt;
    &lt;/div&gt;
    &lt;script&gt;
        console.log('JavaScript代码测试');
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
        </textarea>

        <br><br>
        <button class="btn" onclick="getContent()">获取内容</button>
        <button class="btn" onclick="setTestContent()">设置测试内容</button>
        <button class="btn" onclick="clearContent()">清空</button>

        <div id="result" class="result" style="display: none;">
            <h3>编辑器内容：</h3>
            <pre id="content-display"></pre>
            
            <h3>HTML标签统计：</h3>
            <div id="stats"></div>
        </div>

        <script>
            // 配置CKEditor以支持完整HTML
            CKEDITOR.replace('editor1', {
                // 完全禁用内容过滤
                allowedContent: true,
                
                // 额外允许的内容
                extraAllowedContent: '*(*){*}[*]',
                
                // 保护源代码
                protectedSource: [
                    /<script[\s\S]*?<\/script>/gi,
                    /<style[\s\S]*?<\/style>/gi,
                    /<noscript[\s\S]*?<\/noscript>/gi,
                    /<!--[\s\S]*?-->/gi,
                    /<\?[\s\S]*?\?>/gi,
                    /<%[\s\S]*?%>/gi
                ],
                
                // 禁用实体编码
                entities: false,
                entities_latin: false,
                entities_greek: false,
                entities_processNumerical: false,
                
                // 其他设置
                htmlEncodeOutput: false,
                autoParagraph: false,
                fillEmptyBlocks: false,
                ignoreEmptyParagraph: true,
                
                // 工具栏配置
                toolbar: [
                    { name: 'document', items: ['Source', '-', 'Preview'] },
                    { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
                    { name: 'paragraph', items: ['NumberedList', 'BulletedList'] },
                    { name: 'insert', items: ['Table', 'HorizontalRule'] },
                    { name: 'styles', items: ['Format'] },
                    { name: 'tools', items: ['Maximize'] }
                ]
            });

            function getContent() {
                var content = CKEDITOR.instances.editor1.getData();
                document.getElementById('content-display').textContent = content;
                
                // 统计HTML标签
                var tagMatches = content.match(/<[^>]+>/g);
                var tagCount = tagMatches ? tagMatches.length : 0;
                var hasScript = /<script/i.test(content);
                var hasStyle = /<style/i.test(content);
                var hasDoctype = /<!DOCTYPE/i.test(content);
                
                var stats = `
                    HTML标签总数: ${tagCount}<br>
                    包含&lt;script&gt;标签: ${hasScript ? '是' : '否'}<br>
                    包含&lt;style&gt;标签: ${hasStyle ? '是' : '否'}<br>
                    包含DOCTYPE声明: ${hasDoctype ? '是' : '否'}<br>
                    内容长度: ${content.length} 字符
                `;
                
                document.getElementById('stats').innerHTML = stats;
                document.getElementById('result').style.display = 'block';
            }

            function setTestContent() {
                var testHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>动态测试页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            margin: 0;
            padding: 20px;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .highlight { 
            background: yellow; 
            padding: 5px;
            border-radius: 3px;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>完整HTML测试页面</h1>
        <p>这是一个包含<span class="highlight">完整HTML结构</span>的测试页面。</p>
        
        <h2>功能测试</h2>
        <ul>
            <li>CSS样式支持</li>
            <li>JavaScript交互</li>
            <li>完整HTML结构</li>
        </ul>
        
        <button class="button" onclick="testFunction()">点击测试</button>
        
        <div id="output"></div>
    </div>
    
    <script>
        function testFunction() {
            document.getElementById('output').innerHTML = 
                '<p style="color: green; font-weight: bold;">JavaScript功能正常！时间: ' + 
                new Date().toLocaleString() + '</p>';
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，HTML代码保存功能正常');
        });
    </script>
</body>
</html>`;
                
                CKEDITOR.instances.editor1.setData(testHtml);
            }

            function clearContent() {
                CKEDITOR.instances.editor1.setData('');
                document.getElementById('result').style.display = 'none';
            }
        </script>
    </div>
</body>
</html>
