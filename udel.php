<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

require_once 'config.php';

if (isset($_GET['user'])) {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    $user_to_clear = $_GET['user'];

    $stmt = $conn->prepare("DELETE FROM content WHERE user = ?");
    $stmt->bind_param("s", $user_to_clear);
    $stmt->execute();
    $stmt->close();

    $conn->close();
}

header('Location: user.php');
exit;
?>
