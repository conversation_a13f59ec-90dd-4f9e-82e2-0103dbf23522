<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}
require_once 'config.php';
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// 统计数据
$total_users = $conn->query("SELECT count(*) FROM tab")->fetch_row()[0];
$admin_users = $conn->query("SELECT count(*) FROM tab WHERE ok=1")->fetch_row()[0];
$normal_users = $conn->query("SELECT count(*) FROM tab WHERE ok=2")->fetch_row()[0];

// 获取当前发布的内容
$content_result = $conn->query("SELECT content FROM content WHERE user='system'");
$system_content = $content_result->num_rows > 0 ? $content_result->fetch_assoc()['content'] : '暂无内容发布。';

$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="ckeditor/ckeditor.js"></script>
</head>
<body>
    <div class="sidebar">
        <h2>管理菜单</h2>
        <a href="c.php" class="active">控制台</a>
        <a href="user.php">会员管理</a>
        <a href="add.php">添加会员</a>
        <a href="backup.php">数据备份</a>
        <a href="restore.php">数据恢复</a>
        <a href="close.php">安全退出</a>
    </div>
    <div class="main-content">
        <header>
            <h1>欢迎, <?php echo htmlspecialchars($_SESSION['user']); ?>!</h1>
            <p>当前时间: <?php echo date('Y-m-d H:i:s'); ?></p>
        </header>
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>总会员</h3>
                    <p><?php echo $total_users; ?></p>
                </div>
                <div class="stat-card">
                    <h3>管理员</h3>
                    <p><?php echo $admin_users; ?></p>
                </div>
                <div class="stat-card">
                    <h3>普通用户</h3>
                    <p><?php echo $normal_users; ?></p>
                </div>
            </div>

            <div class="card">
                <h2>发布系统公告</h2>
                <form action="publish.php" method="post">
                    <textarea name="editor1" id="editor1" rows="10" cols="80">
                        <?php echo htmlspecialchars($system_content); ?>
                    </textarea>
                    <br>
                    <button type="submit" class="btn">发布内容</button>
                </form>
                <script>
                    CKEDITOR.replace('editor1');
                </script>
            </div>

            <div class="card">
                <h2>当前公告预览</h2>
                <div class="content-preview">
                    <?php echo $system_content; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
