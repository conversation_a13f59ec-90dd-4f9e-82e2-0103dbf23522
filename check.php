<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

if (!isset($_GET['user'])) {
    header('Location: user.php');
    exit;
}

$target_user = $_GET['user'];

require_once 'config.php';
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get system content
$system_content_result = $conn->query("SELECT content FROM content WHERE user='system'");
$system_content = $system_content_result->num_rows > 0 ? $system_content_result->fetch_assoc()['content'] : '无系统公告。';

// Get user-specific content
$stmt_content = $conn->prepare("SELECT content FROM content WHERE user = ?");
$stmt_content->bind_param("s", $target_user);
$stmt_content->execute();
$user_content_result = $stmt_content->get_result();
$user_content = $user_content_result->num_rows > 0 ? $user_content_result->fetch_assoc()['content'] : '该用户无专属内容。';
$stmt_content->close();

$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容预览: <?php echo htmlspecialchars($target_user); ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto Mono', monospace;
            background: #0a0a14;
            color: #00e5ff;
            margin: 0;
            padding: 20px;
            text-shadow: 0 0 2px #00e5ff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(10, 25, 47, 0.5);
            padding: 40px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 20px #00aaff;
        }
        h1, h2 {
            font-family: 'Orbitron', sans-serif;
            border-bottom: 1px solid #00aaff;
            padding-bottom: 10px;
            color: #fff;
        }
        .content-box {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border: 1px dashed #00aaff;
            line-height: 1.7;
            margin-bottom: 20px;
        }
        .toolbar {
            text-align: right;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: 1px solid #00e5ff;
            background: transparent;
            color: #00e5ff;
            cursor: pointer;
            text-decoration: none;
            margin-left: 10px;
            text-transform: uppercase;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #00e5ff;
            color: #0a0a14;
            box-shadow: 0 0 15px #00e5ff;
            text-shadow: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <button class="btn" onclick="window.print()">打印</button>
            <button class="btn" onclick="copyContent()">复制全部内容</button>
            <button class="btn" onclick="window.close()">关闭页面</button>
        </div>
        <h1>内容预览: <?php echo htmlspecialchars($target_user); ?></h1>
        
        <div id="full-content">
            <h2>系统公告</h2>
            <div class="content-box">
                <?php echo $system_content; ?>
            </div>

            <h2>专属内容</h2>
            <div class="content-box">
                <?php echo $user_content; ?>
            </div>
        </div>
    </div>

    <script>
        function copyContent() {
            const content = document.getElementById('full-content').innerText;
            navigator.clipboard.writeText(content).then(function() {
                alert('内容已复制到剪贴板！');
            }, function(err) {
                alert('复制失败: ', err);
            });
        }
    </script>
</body>
</html>
