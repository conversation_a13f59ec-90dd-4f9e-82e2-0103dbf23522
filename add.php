<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    require_once 'config.php';
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    $user = $_POST['username'];
    $pass = $_POST['password'];
    $pass_confirm = $_POST['password_confirm'];
    $ok = $_POST['ok'];

    if ($pass !== $pass_confirm) {
        $message = '两次输入的密码不匹配！';
        $message_type = 'error';
    } else {
        $stmt = $conn->prepare("SELECT id FROM tab WHERE user = ?");
        $stmt->bind_param("s", $user);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $message = '错误：该用户名已被注册！';
            $message_type = 'error';
        } else {
            $hashed_pass = password_hash($pass, PASSWORD_DEFAULT);
            $date = date('Y-m-d H:i:s');
            
            $insert_stmt = $conn->prepare("INSERT INTO tab (user, pass, date, ok) VALUES (?, ?, ?, ?)");
            $insert_stmt->bind_param("sssi", $user, $hashed_pass, $date, $ok);
            
            if ($insert_stmt->execute()) {
                $message = '新用户添加成功！';
                $message_type = 'success';
            } else {
                $message = '错误：添加用户失败。';
                $message_type = 'error';
            }
            $insert_stmt->close();
        }
        $stmt->close();
    }
    $conn->close();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加会员</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border-radius: 0;
            border: 1px solid #00aaff;
            background: rgba(0, 229, 255, 0.1);
            color: #fff;
            box-sizing: border-box;
            font-family: 'Roboto Mono', monospace;
        }
        .form-group select option {
            background: #0a0a14;
            color: #00e5ff;
        }
        .message {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid;
            text-align: center;
        }
        .message.success {
            background-color: rgba(0, 229, 255, 0.1);
            border-color: #00e5ff;
            color: #fff;
        }
        .message.error {
            background-color: rgba(255, 77, 77, 0.2);
            border-color: #ff4d4d;
            color: #ff4d4d;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>管理菜单</h2>
        <a href="c.php">控制台</a>
        <a href="user.php">会员管理</a>
        <a href="add.php" class="active">添加会员</a>
        <a href="backup.php">数据备份</a>
        <a href="restore.php">数据恢复</a>
        <a href="close.php">安全退出</a>
    </div>
    <div class="main-content">
        <header>
            <h1>添加新会员</h1>
        </header>
        <div class="container">
            <div class="card">
                <?php if ($message): ?>
                    <div class="message <?php echo $message_type; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                <form action="add.php" method="post">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="password_confirm">确认密码</label>
                        <input type="password" id="password_confirm" name="password_confirm" required>
                    </div>
                    <div class="form-group">
                        <label for="ok">用户类型</label>
                        <select id="ok" name="ok">
                            <option value="2">普通用户</option>
                            <option value="1">管理员</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">添加用户</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
