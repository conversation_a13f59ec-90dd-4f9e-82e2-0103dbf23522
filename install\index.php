<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理系统 - 安装程序</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto Mono', monospace;
            background: #0a0a14;
            color: #00e5ff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            text-shadow: 0 0 2px #00e5ff;
        }
        .container {
            background: rgba(10, 25, 47, 0.5);
            padding: 40px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 20px #00aaff, inset 0 0 10px rgba(0, 170, 255, 0.5);
            backdrop-filter: blur(10px);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            font-family: 'Orbitron', sans-serif;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #fff;
            text-shadow: 0 0 5px #fff, 0 0 10px #00e5ff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border-radius: 0;
            border: 1px solid #00aaff;
            background: rgba(0, 229, 255, 0.1);
            color: #fff;
            box-sizing: border-box;
            transition: all 0.3s ease;
            font-family: 'Roboto Mono', monospace;
        }
        .form-group input:focus {
            outline: none;
            border-color: #fff;
            box-shadow: 0 0 10px #00e5ff;
        }
        .btn {
            width: 100%;
            padding: 15px;
            border: 1px solid #00e5ff;
            border-radius: 0;
            background: transparent;
            color: #00e5ff;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .btn:hover {
            background: #00e5ff;
            color: #0a0a14;
            box-shadow: 0 0 15px #00e5ff;
            text-shadow: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统安装</h1>
        <form action="install.php" method="post">
            <div class="form-group">
                <label for="db_host">数据库主机</label>
                <input type="text" id="db_host" name="db_host" value="localhost" required>
            </div>
            <div class="form-group">
                <label for="db_user">数据库用户名</label>
                <input type="text" id="db_user" name="db_user" value="root" required>
            </div>
            <div class="form-group">
                <label for="db_pass">数据库密码</label>
                <input type="password" id="db_pass" name="db_pass" value="111111">
            </div>
            <div class="form-group">
                <label for="db_name">数据库名</label>
                <input type="text" id="db_name" name="db_name" required>
            </div>
            <button type="submit" class="btn">开始安装</button>
        </form>
    </div>
</body>
</html>
