<?php
session_start();
if (!isset($_SESSION['user'])) {
    header('Location: index.php');
    exit;
}

require_once 'config.php';
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$current_user = $_SESSION['user'];

// Get user details
$stmt_user = $conn->prepare("SELECT date, ok FROM tab WHERE user = ?");
$stmt_user->bind_param("s", $current_user);
$stmt_user->execute();
$user_result = $stmt_user->get_result()->fetch_assoc();
$user_reg_date = $user_result['date'];
$user_level = $user_result['ok'];
$stmt_user->close();

// Get system content
$system_content_result = $conn->query("SELECT content FROM content WHERE user='system'");
$system_content = $system_content_result->num_rows > 0 ? $system_content_result->fetch_assoc()['content'] : '欢迎来到本站！';

// Get user-specific content
$stmt_content = $conn->prepare("SELECT content FROM content WHERE user = ?");
$stmt_content->bind_param("s", $current_user);
$stmt_content->execute();
$user_content_result = $stmt_content->get_result();
$user_content = $user_content_result->num_rows > 0 ? $user_content_result->fetch_assoc()['content'] : '';
$stmt_content->close();

$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Overrides for user page */
        body {
            display: block; /* Override flex display for a normal page flow */
        }
        .user-header {
            background: rgba(10, 25, 47, 0.8);
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #00aaff;
            box-shadow: 0 0 15px #00aaff;
        }
        .user-header h1 {
            margin: 0;
            font-size: 24px;
            font-family: 'Orbitron', sans-serif;
        }
        .user-header a {
            color: #00e5ff;
            text-decoration: none;
            padding: 10px 15px;
            border: 1px solid #00e5ff;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        .user-header a:hover {
            background: #00e5ff;
            color: #0a0a14;
            box-shadow: 0 0 15px #00e5ff;
            text-shadow: none;
        }
        .user-main {
            padding: 20px 40px;
        }
        .user-info-card {
            background: rgba(10, 25, 47, 0.6);
            border-radius: 0;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #00aaff;
            box-shadow: inset 0 0 10px rgba(0, 170, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .user-info-card .badge {
            padding: 10px 20px;
            font-weight: bold;
            font-size: 18px;
            font-family: 'Orbitron', sans-serif;
            color: #0a0a14;
            text-shadow: none;
        }
        .badge.admin {
            background: #ff4d4d;
            box-shadow: 0 0 10px #ff4d4d;
        }
        .badge.member {
            background: #00e5ff;
            box-shadow: 0 0 10px #00e5ff;
        }
        .content-box {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border: 1px dashed #00aaff;
            line-height: 1.7;
        }
    </style>
</head>
<body>
    <div class="user-header">
        <h1><?php echo htmlspecialchars($current_user); ?> 的用户中心</h1>
        <a href="close.php">退出登录</a>
    </div>
    <div class="user-main">
        <div class="user-info-card">
            <?php if ($user_level == 1): ?>
                <div class="badge admin">VIP 管理员</div>
            <?php else: ?>
                <div class="badge member">尊贵会员</div>
            <?php endif; ?>
            <div>
                <p><strong>用户名:</strong> <?php echo htmlspecialchars($current_user); ?></p>
                <p><strong>加入时间:</strong> <?php echo date("Y年m月d日", strtotime($user_reg_date)); ?></p>
            </div>
        </div>

        <div class="card">
            <h2>系统公告</h2>
            <div class="content-box">
                <?php echo $system_content; ?>
            </div>
        </div>

        <?php if (!empty($user_content)): ?>
        <div class="card">
            <h2>专属内容</h2>
            <div class="content-box">
                <?php echo $user_content; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
