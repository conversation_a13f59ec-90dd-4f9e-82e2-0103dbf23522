<?php
require_once 'config.php';

try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("连接失败: " . $conn->connect_error);
    }

    echo "<h1>数据库结构检查</h1>";

    // 检查content表结构
    $result = $conn->query("DESCRIBE content");
    if ($result) {
        echo "<h2>content表结构：</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>字段名</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['Type']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查content字段类型
        $contentFieldType = '';
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) {
            if ($row['Field'] == 'content') {
                $contentFieldType = $row['Type'];
                break;
            }
        }
        
        echo "<h3>content字段分析：</h3>";
        if (stripos($contentFieldType, 'text') !== false) {
            if (stripos($contentFieldType, 'longtext') !== false) {
                echo "<span style='color: green;'>✅ LONGTEXT类型，可以存储最大4GB的文本内容</span>";
            } elseif (stripos($contentFieldType, 'mediumtext') !== false) {
                echo "<span style='color: blue;'>ℹ️ MEDIUMTEXT类型，可以存储最大16MB的文本内容</span>";
            } elseif (stripos($contentFieldType, 'text') !== false) {
                echo "<span style='color: orange;'>⚠️ TEXT类型，只能存储最大64KB的文本内容，建议改为LONGTEXT</span>";
            }
        } elseif (stripos($contentFieldType, 'varchar') !== false) {
            echo "<span style='color: red;'>❌ VARCHAR类型，存储容量有限，强烈建议改为LONGTEXT</span>";
        }
    }

    // 检查现有数据
    echo "<h2>现有内容数据：</h2>";
    $result = $conn->query("SELECT user, LENGTH(content) as content_length, LEFT(content, 100) as content_preview FROM content");
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>用户</th><th>内容长度</th><th>内容预览</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['user']) . "</td>";
            echo "<td>" . $row['content_length'] . " 字符</td>";
            echo "<td>" . htmlspecialchars($row['content_preview']) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>暂无内容数据</p>";
    }

    // 提供修改表结构的SQL
    echo "<h2>建议的数据库优化：</h2>";
    if (stripos($contentFieldType, 'longtext') === false) {
        echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007cba;'>";
        echo "<h3>修改content字段为LONGTEXT类型：</h3>";
        echo "<code>ALTER TABLE content MODIFY COLUMN content LONGTEXT;</code>";
        echo "<p><small>执行此SQL命令可以让content字段支持存储更大的HTML内容</small></p>";
        echo "</div>";
    }

    // 测试HTML内容保存
    echo "<h2>HTML内容保存测试：</h2>";
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_content'])) {
        $test_content = $_POST['test_content'];
        
        // 尝试保存测试内容
        $stmt = $conn->prepare("INSERT INTO content (user, content) VALUES (?, ?) ON DUPLICATE KEY UPDATE content = VALUES(content)");
        $test_user = 'html_test_' . date('YmdHis');
        $stmt->bind_param("ss", $test_user, $test_content);
        
        if ($stmt->execute()) {
            echo "<span style='color: green;'>✅ HTML内容保存成功！</span><br>";
            echo "保存的内容长度: " . strlen($test_content) . " 字符<br>";
            
            // 读取并验证
            $verify_stmt = $conn->prepare("SELECT content FROM content WHERE user = ?");
            $verify_stmt->bind_param("s", $test_user);
            $verify_stmt->execute();
            $verify_result = $verify_stmt->get_result();
            
            if ($verify_result && $verify_result->num_rows > 0) {
                $saved_content = $verify_result->fetch_assoc()['content'];
                if ($saved_content === $test_content) {
                    echo "<span style='color: green;'>✅ 内容验证成功，HTML代码完整保存</span><br>";
                } else {
                    echo "<span style='color: red;'>❌ 内容验证失败，保存的内容与原始内容不匹配</span><br>";
                    echo "原始长度: " . strlen($test_content) . ", 保存后长度: " . strlen($saved_content) . "<br>";
                }
            }
            
            // 清理测试数据
            $conn->query("DELETE FROM content WHERE user = '$test_user'");
            
        } else {
            echo "<span style='color: red;'>❌ HTML内容保存失败: " . $stmt->error . "</span><br>";
        }
    }

    $conn->close();

} catch (Exception $e) {
    echo "<span style='color: red;'>错误: " . $e->getMessage() . "</span>";
}
?>

<form method="post">
    <h3>测试HTML内容保存：</h3>
    <textarea name="test_content" rows="10" cols="80">
<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
    <style>
        body { background: #f0f0f0; }
        .test { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <h1>HTML保存测试</h1>
    <p class="test">这是一个包含样式的段落</p>
    <script>
        console.log('JavaScript代码测试');
        alert('HTML保存功能测试');
    </script>
</body>
</html>
    </textarea><br>
    <button type="submit">测试保存HTML内容</button>
</form>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; }
    th, td { text-align: left; padding: 8px; }
    th { background-color: #f2f2f2; }
    code { background: #f8f8f8; padding: 2px 4px; border-radius: 3px; }
</style>
