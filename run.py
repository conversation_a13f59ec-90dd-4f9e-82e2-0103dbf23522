import tkinter as tk
from tkinter import ttk, messagebox
import time
import threading
import os
import platform
import psutil
import keyboard  # 需要安装：pip install keyboard
import win32con  # 需要安装：pip install pywin32
import win32gui
import win32api
import logging
import sys

# 全局变量，保存程序进程句柄，用于后续正常关闭或强制关闭
app_window = None
app_pid = None


def shutdown(wait_time):
    """关机函数"""
    if platform.system() == "Windows":
        try:
            os.system(f"shutdown /s /t {wait_time}")
        except Exception as e:
            logging.error(f"Shutdown failed: {e}")
            messagebox.showerror("Error", "关机失败，请检查权限或系统配置")
    elif platform.system() == "Linux":
        try:
            os.system(f"sudo shutdown -h +{wait_time}")
        except Exception as e:
            logging.error(f"Shutdown failed: {e}")
            messagebox.showerror("Error", "关机失败，请检查权限或系统配置")
    else:
        print("不支持的操作系统")


def sleep_command(wait_time):
    """休眠函数"""
    if platform.system() == "Windows":
        try:
            os.system(f"rundll32.exe powrprof.dll,SetSuspendState 0,1,0")
        except Exception as e:
            logging.error(f"Sleep command failed: {e}")
            messagebox.showerror("Error", "休眠失败，请检查权限或系统配置")
    elif platform.system() == "Linux":
        try:
            os.system(f"systemctl suspend {wait_time}")
        except Exception as e:
            logging.error(f"Sleep command failed: {e}")
            messagebox.showerror("Error", "休眠失败，请检查权限或系统配置")
    else:
        print("不支持的操作系统")


def kill_processes():
    """杀死后台进程"""
    killed_count = 0
    total_processes = len(psutil.process_iter())  # 获取进程总数

    progressbar = ttk.Progressbar(root, orient=tk.HORIZONTAL, length=200, mode='determinate')
    progressbar.pack(pady=10)
    progressbar['maximum'] = total_processes

    for proc in psutil.process_iter():
        try:
            proc.kill()
            killed_count += 1
            progressbar['value'] = killed_count
            root.update_idletasks() # 强制刷新进度条
        except psutil.NoSuchProcess:
            pass
        except psutil.AccessDenied:
            logging.warning(f"无权限杀死进程：{proc.name()}") # 使用warning而不是error，因为这是预期行为
        except Exception as e:
            logging.error(f"杀死进程 {proc.name()} 失败: {e}")

    progressbar.destroy()
    messagebox.showinfo("杀死进程", f"已杀死 {killed_count} 个进程")


def force_shutdown():
    """即时强制关机"""
    if platform.system() == "Windows":
        try:
            win32api.ExitWindowsEx(win32con.EWX_FORCECLOSE)
        except Exception as e:
             logging.error(f"Force shutdown failed: {e}")
             messagebox.showerror("Error", "强制关机失败，请检查权限或系统配置")
    elif platform.system() == "Linux":
        try:
            os.system("sudo shutdown -h now")
        except Exception as e:
            logging.error(f"Force shutdown failed: {e}")
            messagebox.showerror("Error", "强制关机失败，请检查权限或系统配置")
    else:
        print("不支持的操作系统")



def create_ui():
    """创建用户界面"""
    global root, app_window, app_pid
    root = tk.Tk()
    root.title("系统实用工具")
    root.geometry("400x350")  # 增加高度
    root.resizable(False, False)
    root.iconbitmap("icon.ico")  # 添加 icon (需要一个名为 icon.ico 的图标文件)


    # 主题
    style = ttk.Style()
    style.theme_use('clam')  # or 'vista' or 'classic'

    # 随系统启动 设置
    startup_var = tk.BooleanVar()
    startup_check = ttk.Checkbutton(root, text="随系统启动", variable=startup_var)
    startup_check.pack(pady=5)


    # 标签
    tk.Label(root, text="定时关机:", font=("Arial", 12)).pack(pady=5)
    time_entry = tk.Entry(root, width=10)
    time_entry.pack(pady=5)
    tk.Label(root, text="分钟", font=("Arial", 10)).pack(pady=1)

    tk.Label(root, text="倒计时关机:", font=("Arial", 12)).pack(pady=5)
    countdown_label = tk.Label(root, text="0", font=("Arial", 12))
    countdown_label.pack(pady=5)

    tk.Label(root, text="定时休眠:", font=("Arial", 12)).pack(pady=5)
    sleep_time_entry = tk.Entry(root, width=10)
    sleep_time_entry.pack(pady=5)
    tk.Label(root, text="分钟", font=("Arial", 10)).pack(pady=1)


    tk.Label(root, text="倒计时休眠:", font=("Arial", 12)).pack(pady=5)
    countdown_sleep_label = tk.Label(root, text="0", font=("Arial", 12))
    countdown_sleep_label.pack(pady=5)

    # 按钮
    shutdown_button = ttk.Button(root, text="定时关机", command=lambda: shutdown(int(time_entry.get())))
    shutdown_button.pack(pady=5)

    countdown_button = ttk.Button(root, text="倒计时关机", command=lambda: start_countdown(countdown_label))
    countdown_button.pack(pady=5)

    sleep_button = ttk.Button(root, text="定时休眠", command=lambda: sleep_command(int(sleep_time_entry.get())))
    sleep_button.pack(pady=5)

    countdown_sleep_button = ttk.Button(root, text="倒计时休眠", command=lambda: start_countdown(countdown_sleep_label))
    countdown_sleep_button.pack(pady=5)

    kill_processes_button = ttk.Button(root, text="强制杀死所有进程", command=kill_processes)
    kill_processes_button.pack(pady=5)

    force_shutdown_button = ttk.Button(root, text="强制关机", command=force_shutdown)
    force_shutdown_button.pack(pady=5)

    # 快捷键设置
    root.bind("<F1>", lambda event: shutdown_button.invoke()) #F1 执行关机
    root.bind("<F2>", lambda event: countdown_button.invoke()) #F2 执行倒计时关机



    # 快捷键绑定及窗口最小化到任务栏
    root.attributes("-alpha", 0.9) #窗口半透明
    root.iconify()

    # 验证输入
    def validate_input():
        try:
            int(time_entry.get())
            int(sleep_time_entry.get())
            return True
        except ValueError:
             messagebox.showerror("Error", "请输入有效的整数")
             return False


    time_entry.register(validate_input)
    sleep_time_entry.register(validate_input)



def start_countdown(label):
    """倒计时函数"""
    countdown_value = 5
    label.config(text=str(countdown_value))
    root.after(1000, update_countdown, countdown_value, label)


def update_countdown(countdown_value, label):

    if countdown_value > 0:
        countdown_value -= 1
        label.config(text=str(countdown_value))
        root.after(1000, update_countdown, countdown_value, label)
    else:
        if label == countdown_label:
            shutdown(1) # 倒计时结束，关机
        elif label == countdown_sleep_label:
            sleep_command(1) # 倒计时结束，休眠




def on_closing():
    """窗口关闭事件处理函数"""
    if messagebox.askokcancel("退出", "确定要退出吗?"):
        root.destroy()


def on_startup():
    """跟随启动"""

    print("随系统启动") #打印信息



def setup_startup():


    startup_check = ttk.Checkbutton(root, text="随系统启动", variable=startup_var)
    startup_check.pack(pady=5)



def startup_checker():
    pass

def update():
    pass

def main():
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

    root = tk.Tk()
    create_ui()
    root.protocol("WM_CLOSE", on_closing)
    if startup_var.get():
        on_startup()



    root.mainloop()

if __name__ == "__main__":
    main()
