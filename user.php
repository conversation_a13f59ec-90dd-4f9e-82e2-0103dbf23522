<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}
require_once 'config.php';
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Search and filter logic
$search = isset($_GET['search']) ? $conn->real_escape_string($_GET['search']) : '';
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';

$sql = "SELECT id, user, date, ok FROM tab";
$where_clauses = [];
if ($search) {
    $where_clauses[] = "user LIKE '%$search%'";
}
if ($filter == 'admin') {
    $where_clauses[] = "ok = 1";
} elseif ($filter == 'user') {
    $where_clauses[] = "ok = 2";
}

if (count($where_clauses) > 0) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY id DESC";

$result = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Additional styles for user management page */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .toolbar input, .toolbar select {
            padding: 10px;
            border-radius: 0;
            border: 1px solid #00aaff;
            background: rgba(0, 229, 255, 0.1);
            color: #fff;
            font-family: 'Roboto Mono', monospace;
        }
        .toolbar select option {
            background: #0a0a14;
            color: #00e5ff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #00aaff;
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #00aaff;
            border-right: 1px solid #00aaff;
        }
        th {
            background: rgba(0, 170, 255, 0.2);
            font-family: 'Orbitron', sans-serif;
            color: #fff;
        }
        .actions a {
            color: #0a0a14;
            padding: 5px 10px;
            border-radius: 0;
            text-decoration: none;
            margin-right: 5px;
            transition: all 0.2s ease-in-out;
            display: inline-block;
            text-shadow: none;
        }
        .actions .delete { background-color: #ff4d4d; }
        .actions .delete:hover { box-shadow: 0 0 10px #ff4d4d; }
        .actions .edit { background-color: #00e5ff; }
        .actions .edit:hover { box-shadow: 0 0 10px #00e5ff; }
        .actions .clear { background-color: #f1c40f; }
        .actions .clear:hover { box-shadow: 0 0 10px #f1c40f; }
        .actions .view { background-color: #2ecc71; }
        .actions .view:hover { box-shadow: 0 0 10px #2ecc71; }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>管理菜单</h2>
        <a href="c.php">控制台</a>
        <a href="user.php" class="active">会员管理</a>
        <a href="add.php">添加会员</a>
        <a href="backup.php">数据备份</a>
        <a href="restore.php">数据恢复</a>
        <a href="close.php">安全退出</a>
    </div>
    <div class="main-content">
        <header>
            <h1>会员管理</h1>
        </header>
        <div class="container">
            <div class="card">
                <div class="toolbar">
                    <form method="get" action="user.php">
                        <input type="text" name="search" placeholder="搜索用户名..." value="<?php echo htmlspecialchars($search); ?>">
                        <select name="filter" onchange="this.form.submit()">
                            <option value="all" <?php if($filter == 'all') echo 'selected'; ?>>所有用户</option>
                            <option value="admin" <?php if($filter == 'admin') echo 'selected'; ?>>仅管理员</option>
                            <option value="user" <?php if($filter == 'user') echo 'selected'; ?>>仅普通用户</option>
                        </select>
                        <button type="submit" class="btn">搜索</button>
                    </form>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>注册时间</th>
                            <th>权限</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while($row = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $row['id']; ?></td>
                                    <td><?php echo htmlspecialchars($row['user']); ?></td>
                                    <td><?php echo $row['date']; ?></td>
                                    <td><?php echo $row['ok'] == 1 ? '管理员' : '普通用户'; ?></td>
                                    <td class="actions">
                                        <a href="u.php?user=<?php echo urlencode($row['user']); ?>" class="edit">指定内容</a>
                                        <a href="check.php?user=<?php echo urlencode($row['user']); ?>" class="view" target="_blank">查看内容</a>
                                        <a href="udel.php?user=<?php echo urlencode($row['user']); ?>" class="clear" onclick="return confirm('确定要清空该用户的专属内容吗？');">清空内容</a>
                                        <a href="del.php?id=<?php echo $row['id']; ?>" class="delete" onclick="return confirm('确定要删除这个用户吗？此操作不可恢复！');">删除</a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" style="text-align:center;">没有找到用户。</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
<?php $conn->close(); ?>
