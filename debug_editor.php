<?php
session_start();

// 简单的调试页面，不需要登录验证
$saved_content = '';
$debug_info = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['editor1'])) {
    $saved_content = $_POST['editor1'];
    $debug_info = "原始POST数据长度: " . strlen($saved_content) . " 字符\n";
    $debug_info .= "是否包含HTML标签: " . (preg_match('/<[^>]+>/', $saved_content) ? '是' : '否') . "\n";
    $debug_info .= "HTML标签数量: " . preg_match_all('/<[^>]+>/', $saved_content, $matches) . "\n";
    
    // 保存到文件进行调试
    file_put_contents('debug_content.txt', $saved_content);
    $debug_info .= "内容已保存到 debug_content.txt 文件\n";
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML代码保存调试</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            background: #f0f8ff;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #007cba;
            border-radius: 4px;
        }
        .result-section {
            background: #f0fff0;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
            border-radius: 4px;
        }
        .error-section {
            background: #fff0f0;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
            border-radius: 4px;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HTML代码保存功能调试</h1>
        
        <div class="debug-section">
            <h3>🔧 调试说明</h3>
            <p>1. 在编辑器中输入HTML代码（可以点击Source按钮直接编辑）</p>
            <p>2. 点击"保存并调试"按钮</p>
            <p>3. 查看下方的调试信息，确认HTML代码是否被正确保存</p>
            <p>4. 使用预设的测试HTML代码进行快速测试</p>
        </div>

        <form method="post">
            <textarea name="editor1" id="editor1" rows="15" cols="80">
&lt;!DOCTYPE html&gt;
&lt;html lang="zh-CN"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;title&gt;测试网页&lt;/title&gt;
    &lt;style&gt;
        body { font-family: Arial, sans-serif; }
        .highlight { background-color: yellow; }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;这是一个测试网页&lt;/h1&gt;
    &lt;p class="highlight"&gt;这段文字有高亮背景&lt;/p&gt;
    &lt;script&gt;
        console.log('JavaScript代码测试');
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
            </textarea>
            <br><br>
            <button type="submit" class="btn">保存并调试</button>
            <button type="button" class="btn" onclick="loadTestContent()">加载测试HTML</button>
            <button type="button" class="btn" onclick="clearEditor()">清空编辑器</button>
            <button type="button" class="btn btn-danger" onclick="showRawContent()">显示原始内容</button>
        </form>

        <?php if ($_SERVER['REQUEST_METHOD'] == 'POST'): ?>
        <div class="result-section">
            <h3>✅ 调试结果</h3>
            <pre><?php echo htmlspecialchars($debug_info); ?></pre>
        </div>

        <div class="result-section">
            <h3>📝 保存的内容</h3>
            <pre><?php echo htmlspecialchars($saved_content); ?></pre>
        </div>

        <div class="result-section">
            <h3>🎨 渲染效果</h3>
            <iframe srcdoc="<?php echo htmlspecialchars($saved_content); ?>" 
                    style="width: 100%; height: 300px; border: 1px solid #ccc;"></iframe>
        </div>
        <?php endif; ?>

        <script>
            // 使用更强的配置初始化CKEditor
            CKEDITOR.replace('editor1', {
                allowedContent: true,
                extraAllowedContent: '*(*){*}[*]',
                protectedSource: [
                    /<script[\s\S]*?<\/script>/gi,
                    /<style[\s\S]*?<\/style>/gi,
                    /<!--[\s\S]*?-->/gi,
                    /<\?[\s\S]*?\?>/gi,
                    /<%[\s\S]*?%>/gi
                ],
                entities: false,
                htmlEncodeOutput: false,
                autoParagraph: false,
                fillEmptyBlocks: false,
                ignoreEmptyParagraph: true
            });

            function loadTestContent() {
                var testHtml = '<!DOCTYPE html>\n' +
                              '<html>\n' +
                              '<head>\n' +
                              '    <title>完整网页测试</title>\n' +
                              '    <style>\n' +
                              '        body { background: #f0f0f0; }\n' +
                              '        .container { max-width: 800px; margin: 0 auto; }\n' +
                              '    </style>\n' +
                              '</head>\n' +
                              '<body>\n' +
                              '    <div class="container">\n' +
                              '        <h1>测试标题</h1>\n' +
                              '        <p>这是一个包含<strong>HTML标签</strong>的段落。</p>\n' +
                              '        <script>\n' +
                              '            alert("JavaScript测试");\n' +
                              '        </script>\n' +
                              '    </div>\n' +
                              '</body>\n' +
                              '</html>';
                
                CKEDITOR.instances.editor1.setData(testHtml);
            }

            function clearEditor() {
                CKEDITOR.instances.editor1.setData('');
            }

            function showRawContent() {
                var content = CKEDITOR.instances.editor1.getData();
                alert('编辑器原始内容:\n\n' + content);
            }
        </script>
    </div>
</body>
</html>
