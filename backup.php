<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

$backup_dir = 'backups/';
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

$message = '';
$message_type = '';

// Handle backup creation
if (isset($_GET['action']) && $_GET['action'] == 'create') {
    require_once 'config.php';
    
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($mysqli->connect_error) {
        $message = "数据库连接失败: " . $mysqli->connect_error;
        $message_type = 'error';
    } else {
        $mysqli->set_charset('utf8mb4');
        
        $tables = array();
        $result = $mysqli->query('SHOW TABLES');
        while ($row = $result->fetch_row()) {
            $tables[] = $row[0];
        }
        
        $sql_script = "";
        foreach ($tables as $table) {
            $result = $mysqli->query('SELECT * FROM ' . $table);
            $num_fields = $result->field_count;
            
            $sql_script .= 'DROP TABLE IF EXISTS ' . $table . ';';
            $row2 = $mysqli->query('SHOW CREATE TABLE ' . $table)->fetch_row();
            $sql_script .= "\n\n" . $row2[1] . ";\n\n";
            
            for ($i = 0; $i < $num_fields; $i++) {
                while ($row = $result->fetch_row()) {
                    $sql_script .= 'INSERT INTO ' . $table . ' VALUES(';
                    for ($j = 0; $j < $num_fields; $j++) {
                        $row[$j] = $mysqli->real_escape_string($row[$j]);
                        if (isset($row[$j])) {
                            $sql_script .= '"' . $row[$j] . '"';
                        } else {
                            $sql_script .= '""';
                        }
                        if ($j < ($num_fields - 1)) {
                            $sql_script .= ',';
                        }
                    }
                    $sql_script .= ");\n";
                }
            }
            $sql_script .= "\n";
        }
        
        if (!empty($sql_script)) {
            $backup_file_name = $backup_dir . 'backup_' . date("Y-m-d_H-i-s") . '.sql';
            if (file_put_contents($backup_file_name, $sql_script)) {
                $message = "数据库备份成功！文件保存在: " . $backup_file_name;
                $message_type = 'success';
            } else {
                $message = "错误：无法写入备份文件。";
                $message_type = 'error';
            }
        }
        $mysqli->close();
    }
}

// Handle file deletion
if (isset($_GET['delete'])) {
    $file_to_delete = $backup_dir . basename($_GET['delete']);
    if (file_exists($file_to_delete) && strpos($file_to_delete, '..') === false) {
        unlink($file_to_delete);
        $message = "备份文件 " . htmlspecialchars(basename($_GET['delete'])) . " 已删除。";
        $message_type = 'success';
    } else {
        $message = "错误：文件不存在或无效的文件名。";
        $message_type = 'error';
    }
}

// List backup files
$backup_files = array_diff(scandir($backup_dir), array('..', '.'));

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据备份</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        table { width: 100%; border-collapse: collapse; border: 1px solid #00aaff; }
        th, td { padding: 15px; text-align: left; border-bottom: 1px solid #00aaff; border-right: 1px solid #00aaff; }
        th { background: rgba(0, 170, 255, 0.2); font-family: 'Orbitron', sans-serif; color: #fff; }
        .actions a { color: #0a0a14; padding: 5px 10px; border-radius: 0; text-decoration: none; margin-right: 5px; transition: all 0.2s ease-in-out; display: inline-block; text-shadow: none; }
        .actions .download { background-color: #00e5ff; }
        .actions .download:hover { box-shadow: 0 0 10px #00e5ff; }
        .actions .delete { background-color: #ff4d4d; }
        .actions .delete:hover { box-shadow: 0 0 10px #ff4d4d; }
        .message { padding: 15px; margin-bottom: 20px; border: 1px solid; text-align: center; }
        .message.success { background-color: rgba(0, 229, 255, 0.1); border-color: #00e5ff; color: #fff; }
        .message.error { background-color: rgba(255, 77, 77, 0.2); border-color: #ff4d4d; color: #ff4d4d; }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>管理菜单</h2>
        <a href="c.php">控制台</a>
        <a href="user.php">会员管理</a>
        <a href="add.php">添加会员</a>
        <a href="backup.php" class="active">数据备份</a>
        <a href="restore.php">数据恢复</a>
        <a href="close.php">安全退出</a>
    </div>
    <div class="main-content">
        <header>
            <h1>数据备份</h1>
        </header>
        <div class="container">
            <div class="card">
                <a href="backup.php?action=create" class="btn">创建新的数据库备份</a>
            </div>
            <div class="card">
                <h2>现有备份</h2>
                <?php if ($message): ?>
                    <div class="message <?php echo $message_type; ?>"><?php echo $message; ?></div>
                <?php endif; ?>
                <table>
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>文件大小</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($backup_files) > 0): ?>
                            <?php foreach ($backup_files as $file): ?>
                                <?php $file_path = $backup_dir . $file; ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($file); ?></td>
                                    <td><?php echo round(filesize($file_path) / 1024, 2); ?> KB</td>
                                    <td><?php echo date("Y-m-d H:i:s", filemtime($file_path)); ?></td>
                                    <td class="actions">
                                        <a href="<?php echo $file_path; ?>" class="download" download>下载</a>
                                        <a href="backup.php?delete=<?php echo urlencode($file); ?>" class="delete" onclick="return confirm('确定要删除这个备份文件吗？');">删除</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" style="text-align:center;">没有找到备份文件。</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
