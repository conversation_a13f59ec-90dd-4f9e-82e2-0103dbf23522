<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

require_once 'config.php';

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // First, get the username to delete their content
    $user_stmt = $conn->prepare("SELECT user FROM tab WHERE id = ?");
    $user_stmt->bind_param("i", $id);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    if ($user_row = $user_result->fetch_assoc()) {
        $user_to_delete = $user_row['user'];

        // Delete from content table
        $content_stmt = $conn->prepare("DELETE FROM content WHERE user = ?");
        $content_stmt->bind_param("s", $user_to_delete);
        $content_stmt->execute();
        $content_stmt->close();
    }
    $user_stmt->close();


    // Then, delete from tab table
    $stmt = $conn->prepare("DELETE FROM tab WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->close();

    $conn->close();
}

header('Location: user.php');
exit;
?>
