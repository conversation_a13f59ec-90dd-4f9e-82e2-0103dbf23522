<?php
session_start();

// 检查是否已安装，如果未安装则跳转到安装页面
if (!file_exists('config.php')) {
    header('Location: install/index.php');
    exit;
}

require_once 'config.php';

// 如果已登录，则根据用户类型跳转
if (isset($_SESSION['user'])) {
    if ($_SESSION['ok'] == 1) {
        header('Location: c.php'); // 管理员
    } else {
        header('Location: e.php'); // 普通用户
    }
    exit;
}

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    $user = $_POST['username'];
    $pass = $_POST['password'];

    $stmt = $conn->prepare("SELECT pass, ok FROM tab WHERE user = ?");
    $stmt->bind_param("s", $user);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        $stmt->bind_result($hashed_pass, $ok);
        $stmt->fetch();
        if (password_verify($pass, $hashed_pass)) {
            $_SESSION['user'] = $user;
            $_SESSION['ok'] = $ok;
            if ($ok == 1) {
                header('Location: c.php');
            } else {
                header('Location: e.php');
            }
            exit;
        } else {
            $error_message = '用户名或密码错误！';
        }
    } else {
        $error_message = '用户名或密码错误！';
    }

    $stmt->close();
    $conn->close();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员登录</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto Mono', monospace;
            background: #0a0a14;
            color: #00e5ff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            text-shadow: 0 0 2px #00e5ff;
        }
        .container {
            background: rgba(10, 25, 47, 0.5);
            padding: 40px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 20px #00aaff, inset 0 0 10px rgba(0, 170, 255, 0.5);
            backdrop-filter: blur(10px);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            font-family: 'Orbitron', sans-serif;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #fff;
            text-shadow: 0 0 5px #fff, 0 0 10px #00e5ff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border-radius: 0;
            border: 1px solid #00aaff;
            background: rgba(0, 229, 255, 0.1);
            color: #fff;
            box-sizing: border-box;
            transition: all 0.3s ease;
            font-family: 'Roboto Mono', monospace;
        }
        .form-group input:focus {
            outline: none;
            border-color: #fff;
            box-shadow: 0 0 10px #00e5ff;
        }
        .btn {
            width: 100%;
            padding: 15px;
            border: 1px solid #00e5ff;
            border-radius: 0;
            background: transparent;
            color: #00e5ff;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .btn:hover {
            background: #00e5ff;
            color: #0a0a14;
            box-shadow: 0 0 15px #00e5ff;
            text-shadow: none;
        }
        .error {
            color: #ff4d4d;
            text-align: center;
            margin-bottom: 15px;
            border: 1px dashed #ff4d4d;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>会员登录</h1>
        <?php if ($error_message): ?>
            <p class="error"><?php echo $error_message; ?></p>
        <?php endif; ?>
        <form action="index.php" method="post">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn">登录</button>
        </form>
    </div>
</body>
</html>
