@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Roboto+Mono:wght@400;700&display=swap');

/* General Body Styles */
body {
    font-family: 'Roboto Mono', monospace;
    background: #0a0a14; /* Deep space blue/black */
    color: #00e5ff; /* Bright cyan text */
    margin: 0;
    display: flex;
    text-shadow: 0 0 2px #00e5ff;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background: rgba(10, 25, 47, 0.5); /* Dark blue transparent */
    backdrop-filter: blur(10px);
    border-right: 1px solid #00aaff;
    height: 100vh;
    position: fixed;
    padding-top: 20px;
    box-shadow: 0 0 15px #00aaff;
}

.sidebar h2 {
    font-family: 'Orbitron', sans-serif;
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    text-shadow: 0 0 5px #fff, 0 0 10px #00e5ff;
}

.sidebar a {
    display: block;
    color: #00e5ff;
    padding: 15px 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar a:hover {
    background: rgba(0, 229, 255, 0.1);
    color: #fff;
    border-left: 3px solid #00e5ff;
    text-shadow: 0 0 5px #fff;
}

.sidebar a.active {
    background: #00e5ff;
    color: #0a0a14;
    border-left: 3px solid #fff;
    text-shadow: none;
    box-shadow: 0 0 10px #00e5ff;
}

/* Main Content Styles */
.main-content {
    margin-left: 250px;
    padding: 20px 40px;
    width: calc(100% - 250px);
}

header {
    padding-bottom: 20px;
    border-bottom: 1px solid #00aaff;
    margin-bottom: 20px;
}

header h1 {
    font-family: 'Orbitron', sans-serif;
    margin: 0;
    color: #fff;
    text-shadow: 0 0 5px #fff;
}

header p {
    margin: 5px 0 0;
    color: #00aaff;
}

.container {
    width: 100%;
}

/* Card Styles */
.card {
    background: rgba(10, 25, 47, 0.6);
    border-radius: 0; /* Sharp corners */
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid #00aaff;
    box-shadow: inset 0 0 10px rgba(0, 170, 255, 0.5);
}

.card h2 {
    font-family: 'Orbitron', sans-serif;
    margin-top: 0;
    border-bottom: 1px solid #00aaff;
    padding-bottom: 10px;
    margin-bottom: 20px;
    color: #fff;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: transparent;
    padding: 20px;
    text-align: center;
    border: 1px solid #00e5ff;
    box-shadow: 0 0 10px #00e5ff;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(0, 229, 255, 0.1);
    transform: translateY(-5px);
}

.stat-card h3 {
    font-family: 'Orbitron', sans-serif;
    margin: 0 0 10px;
    font-size: 18px;
    color: #fff;
}

.stat-card p {
    margin: 0;
    font-size: 32px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 8px #fff;
}

/* Form and Button Styles */
.btn {
    padding: 12px 25px;
    border: 1px solid #00e5ff;
    border-radius: 0;
    background: transparent;
    color: #00e5ff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.btn:hover {
    background: #00e5ff;
    color: #0a0a14;
    box-shadow: 0 0 15px #00e5ff;
    text-shadow: none;
}

/* Content Preview */
.content-preview {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border: 1px dashed #00aaff;
    min-height: 100px;
    line-height: 1.6;
}

/* CKEditor specific styles */
.cke_chrome {
    border-radius: 0 !important;
    border: 1px solid #00aaff !important;
    box-shadow: 0 0 10px #00aaff !important;
}
.cke_top {
    background: #0a192f !important;
    border-bottom: 1px solid #00aaff !important;
}
.cke_bottom {
    background: #0a192f !important;
}
.cke_toolgroup a.cke_button:hover, .cke_toolgroup a.cke_button:focus, .cke_toolgroup a.cke_button:active {
    background: #00e5ff !important;
    color: #0a0a14 !important;
}
.cke_combo_button {
    background: #0a192f !important;
}
.cke_editable {
    background: #0a0a14 !important;
    color: #00e5ff !important;
    text-shadow: 0 0 1px #00e5ff;
}
