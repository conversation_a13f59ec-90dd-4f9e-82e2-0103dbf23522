<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装状态</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Roboto+Mono&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto Mono', monospace;
            background: #0a0a14;
            color: #00e5ff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            text-align: center;
            text-shadow: 0 0 2px #00e5ff;
        }
        .container {
            background: rgba(10, 25, 47, 0.5);
            padding: 40px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 20px #00aaff, inset 0 0 10px rgba(0, 170, 255, 0.5);
            backdrop-filter: blur(10px);
            width: 100%;
            max-width: 600px;
        }
        h1 {
            font-family: 'Orbitron', sans-serif;
            color: #fff;
            text-shadow: 0 0 5px #fff, 0 0 10px #00e5ff;
        }
        .message {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid;
            text-align: left;
        }
        .message.success {
            background-color: rgba(0, 229, 255, 0.1);
            border-color: #00e5ff;
            color: #fff;
        }
        .message.error {
            background-color: rgba(255, 77, 77, 0.2);
            border-color: #ff4d4d;
            color: #ff4d4d;
        }
        a {
            color: #fff;
            text-decoration: none;
            font-weight: bold;
            border: 1px solid #00e5ff;
            padding: 10px 20px;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        a:hover {
            background: #00e5ff;
            color: #0a0a14;
            box-shadow: 0 0 15px #00e5ff;
            text-shadow: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>安装状态</h1>
        <?php
        if (file_exists('install.lock')) {
            echo '<div class="message error">安装程序已被锁定。如果您需要重新安装，请删除 install/install.lock 文件。</div>';
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $db_host = $_POST['db_host'];
            $db_user = $_POST['db_user'];
            $db_pass = $_POST['db_pass'];
            $db_name = $_POST['db_name'];

            // 1. Create config file
            $config_content = "<?php\n";
            $config_content .= "define('DB_HOST', '" . $db_host . "');\n";
            $config_content .= "define('DB_USER', '" . $db_user . "');\n";
            $config_content .= "define('DB_PASS', '" . $db_pass . "');\n";
            $config_content .= "define('DB_NAME', '" . $db_name . "');\n";
            $config_content .= "?>";

            if (!file_put_contents('../config.php', $config_content)) {
                echo '<div class="message error">错误：无法创建配置文件 ../config.php。请检查目录权限。</div>';
                exit;
            }
            echo '<div class="message success">配置文件 config.php 创建成功。</div>';

            // 2. Connect and create database
            $conn = new mysqli($db_host, $db_user, $db_pass);
            if ($conn->connect_error) {
                echo '<div class="message error">数据库连接失败: ' . $conn->connect_error . '</div>';
                unlink('../config.php');
                exit;
            }
            echo '<div class="message success">数据库服务器连接成功。</div>';

            $sql_create_db = "CREATE DATABASE IF NOT EXISTS `$db_name` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if ($conn->query($sql_create_db) === TRUE) {
                echo '<div class="message success">数据库 ' . $db_name . ' 创建成功或已存在。</div>';
            } else {
                echo '<div class="message error">数据库创建失败: ' . $conn->error . '</div>';
                unlink('../config.php');
                $conn->close();
                exit;
            }
            $conn->select_db($db_name);

            // 3. Create tables
            $sql_create_tab = "
            CREATE TABLE IF NOT EXISTS `tab` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user` varchar(255) NOT NULL,
              `pass` varchar(255) NOT NULL,
              `date` datetime NOT NULL,
              `ok` int(11) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            $sql_create_content = "
            CREATE TABLE IF NOT EXISTS `content` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user` varchar(255) NOT NULL,
              `content` text,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            if ($conn->query($sql_create_tab) === TRUE) {
                echo '<div class="message success">数据表 `tab` 创建成功。</div>';
            } else {
                echo '<div class="message error">数据表 `tab` 创建失败: ' . $conn->error . '</div>';
                $conn->close();
                exit;
            }

            if ($conn->query($sql_create_content) === TRUE) {
                echo '<div class="message success">数据表 `content` 创建成功。</div>';
            } else {
                echo '<div class="message error">数据表 `content` 创建失败: ' . $conn->error . '</div>';
                $conn->close();
                exit;
            }
            
            // 4. Create default admin
            $admin_user = 'admin';
            $admin_pass = password_hash('123456', PASSWORD_DEFAULT);
            $admin_date = date('Y-m-d H:i:s');
            $stmt = $conn->prepare("INSERT INTO tab (user, pass, date, ok) VALUES (?, ?, ?, 1)");
            $stmt->bind_param("sss", $admin_user, $admin_pass, $admin_date);
            if ($stmt->execute()) {
                echo '<div class="message success">默认管理员账号创建成功 (用户名: admin, 密码: 123456)。</div>';
            } else {
                echo '<div class="message error">创建默认管理员失败: ' . $stmt->error . '</div>';
            }
            $stmt->close();
            $conn->close();

            // 5. Create lock file
            file_put_contents('install.lock', 'installed');
            echo '<div class="message success">安装成功！为了安全，安装程序已被锁定。</div>';
            echo '<a href="../index.php">点击这里访问网站首页</a>';

        } else {
            echo '<div class="message error">无效的请求。</div>';
        }
        ?>
    </div>
</body>
</html>
