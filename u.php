<?php
session_start();
if (!isset($_SESSION['user']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit;
}

if (!isset($_GET['user'])) {
    header('Location: user.php');
    exit;
}

$target_user = $_GET['user'];

require_once 'config.php';
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user-specific content
$stmt = $conn->prepare("SELECT content FROM content WHERE user = ?");
$stmt->bind_param("s", $target_user);
$stmt->execute();
$result = $stmt->get_result();
$user_content = $result->num_rows > 0 ? $result->fetch_assoc()['content'] : '';
$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>为 <?php echo htmlspecialchars($target_user); ?> 指定内容</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="ckeditor/ckeditor.js"></script>
</head>
<body>
    <div class="sidebar">
        <h2>管理菜单</h2>
        <a href="c.php">控制台</a>
        <a href="user.php" class="active">会员管理</a>
        <a href="add.php">添加会员</a>
        <a href="backup.php">数据备份</a>
        <a href="restore.php">数据恢复</a>
        <a href="close.php">安全退出</a>
    </div>
    <div class="main-content">
        <header>
            <h1>为用户 "<?php echo htmlspecialchars($target_user); ?>" 指定专属内容</h1>
        </header>
        <div class="container">
            <div class="card">
                <form action="uu.php" method="post">
                    <input type="hidden" name="target_user" value="<?php echo htmlspecialchars($target_user); ?>">
                    <textarea name="editor1" id="editor1" rows="10" cols="80">
                        <?php echo $user_content; ?>
                    </textarea>
                    <br>
                    <button type="submit" class="btn">保存内容</button>
                    <a href="user.php" style="margin-left: 15px; color: #fff; text-decoration: none;">返回</a>
                </form>
                <script>
                    CKEDITOR.replace('editor1');
                </script>
            </div>
        </div>
    </div>
</body>
</html>
