﻿/*
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

CKEDITOR.editorConfig = function( config )
{
	// Define changes to default configuration here. For example:
	// config.language = 'fr';
	// config.uiColor = '#AADC6E';

	// 添加字体大小功能到默认工具栏，包含Source按钮用于HTML编辑
	config.toolbar = [
		{ name: 'document', items: ['Source', '-', 'Save', 'NewPage', 'Preview', 'Print', '-', 'Templates'] },
		{ name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
		{ name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl'] },
		{ name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
		{ name: 'insert', items: ['Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe'] },
		'/',
		{ name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
		{ name: 'colors', items: ['TextColor', 'BGColor'] },
		{ name: 'tools', items: ['Maximize', 'ShowBlocks', '-', 'About'] }
	];

	// 设置字体大小选项
	config.fontSize_sizes = '8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px';
	config.fontSize_defaultLabel = '12px';

	// 完全禁用高级内容过滤器(ACF)，允许所有HTML内容
	config.allowedContent = true;

	// 禁用所有内容过滤
	config.fullPage = false;
	config.extraAllowedContent = '*(*){*}[*]';

	// 保护源代码，防止被过滤
	config.protectedSource = [
		/<script[\s\S]*?<\/script>/gi,
		/<style[\s\S]*?<\/style>/gi,
		/<noscript[\s\S]*?<\/noscript>/gi,
		/<!--[\s\S]*?-->/gi,
		/<\?[\s\S]*?\?>/gi,
		/<%[\s\S]*?%>/gi,
		/<asp:[\s\S]*?<\/asp:[^>]*>/gi,
		/<asp:[^>]*\/>/gi
	];

	// 确保不会自动清理粘贴的内容
	config.pasteFromWordRemoveFontStyles = false;
	config.pasteFromWordRemoveStyles = false;
	config.pasteFromWordCleanupFile = '';

	// 禁用自动段落包装
	config.autoParagraph = false;

	// 保持原始HTML格式
	config.entities = false;
	config.entities_latin = false;
	config.entities_greek = false;
	config.entities_processNumerical = false;

	// 禁用HTML编码
	config.htmlEncodeOutput = false;

	// 保持源代码格式
	config.ignoreEmptyParagraph = false;
};
