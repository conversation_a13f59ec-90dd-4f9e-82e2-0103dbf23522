<?php
// 检查PHP配置，查看可能影响HTML内容保存的设置

echo "<h1>PHP配置检查</h1>";

echo "<h2>Magic Quotes 设置</h2>";
if (function_exists('get_magic_quotes_gpc')) {
    echo "get_magic_quotes_gpc(): " . (get_magic_quotes_gpc() ? '开启' : '关闭') . "<br>";
} else {
    echo "get_magic_quotes_gpc(): 函数不存在（PHP 7.4+已移除）<br>";
}

if (function_exists('get_magic_quotes_runtime')) {
    echo "get_magic_quotes_runtime(): " . (get_magic_quotes_runtime() ? '开启' : '关闭') . "<br>";
} else {
    echo "get_magic_quotes_runtime(): 函数不存在（PHP 7.4+已移除）<br>";
}

echo "<h2>相关PHP设置</h2>";
echo "PHP版本: " . phpversion() . "<br>";
echo "default_charset: " . ini_get('default_charset') . "<br>";
echo "mbstring.internal_encoding: " . ini_get('mbstring.internal_encoding') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "max_input_vars: " . ini_get('max_input_vars') . "<br>";

echo "<h2>测试POST数据处理</h2>";
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_html'])) {
    $test_content = $_POST['test_html'];
    echo "<h3>接收到的数据：</h3>";
    echo "<pre>" . htmlspecialchars($test_content) . "</pre>";
    
    echo "<h3>数据分析：</h3>";
    echo "长度: " . strlen($test_content) . " 字符<br>";
    echo "是否包含HTML标签: " . (preg_match('/<[^>]+>/', $test_content) ? '是' : '否') . "<br>";
    echo "HTML标签数量: " . preg_match_all('/<[^>]+>/', $test_content, $matches) . "<br>";
    
    // 检查是否有反斜杠被添加
    if (strpos($test_content, '\\"') !== false || strpos($test_content, "\\'") !== false) {
        echo "<span style='color: red;'>警告: 检测到转义字符，可能是magic_quotes导致的</span><br>";
    }
}
?>

<form method="post">
    <h3>测试HTML内容提交：</h3>
    <textarea name="test_html" rows="10" cols="80">
&lt;div class="test"&gt;
    &lt;h1&gt;测试标题&lt;/h1&gt;
    &lt;p style="color: red;"&gt;带样式的段落&lt;/p&gt;
    &lt;script&gt;alert('test');&lt;/script&gt;
&lt;/div&gt;
    </textarea><br>
    <button type="submit">提交测试</button>
</form>

<h2>建议的解决方案</h2>
<ul>
    <li>确保magic_quotes_gpc关闭（现代PHP版本已默认关闭）</li>
    <li>在保存前不要使用htmlspecialchars()或strip_tags()</li>
    <li>使用prepared statements防止SQL注入（已经在使用）</li>
    <li>确保数据库字段类型支持大文本（TEXT或LONGTEXT）</li>
</ul>
